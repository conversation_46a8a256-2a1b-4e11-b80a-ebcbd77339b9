import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MessageSquare, 
  Palette, 
  Sparkles, 
  Eye, 
  Download, 
  Shirt,
  ArrowRight,
  CheckCircle,
  Lightbulb,
  Zap
} from "lucide-react";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "How It Works - AI T-Shirt Design Generator | AI Shirt",
  description: "Learn how to create stunning T-shirt designs with AI in just 5 simple steps. No design experience needed - from idea to print-ready design in seconds.",
  keywords: "how to design t-shirt, ai t-shirt generator tutorial, custom t-shirt design guide, t-shirt design steps",
};

const STEPS = [
  {
    step: 1,
    title: "Describe Your Idea",
    description: "Simply type what you want on your T-shirt. Be as creative and detailed as you like!",
    icon: MessageSquare,
    example: "A cute cat wearing sunglasses with rainbow background",
    tip: "The more specific you are, the better your design will be!"
  },
  {
    step: 2,
    title: "Choose Your Style",
    description: "Select from our variety of art styles to match your vision perfectly.",
    icon: Palette,
    example: "Minimalist, Vintage, Modern, Cartoon, or Abstract",
    tip: "Each style gives your design a unique personality and feel."
  },
  {
    step: 3,
    title: "Generate with AI",
    description: "Our advanced AI creates your unique design in seconds. Magic happens here!",
    icon: Sparkles,
    example: "AI processes your prompt and creates stunning artwork",
    tip: "Don't like the first result? Generate again for different variations!"
  },
  {
    step: 4,
    title: "Preview on T-Shirt",
    description: "See exactly how your design will look on an actual T-shirt before downloading.",
    icon: Eye,
    example: "Realistic mockup shows your design on different shirt colors",
    tip: "Check how your design looks on both light and dark shirts."
  },
  {
    step: 5,
    title: "Download & Print",
    description: "Get your high-resolution, print-ready design file instantly.",
    icon: Download,
    example: "High-quality PNG perfect for any printing service",
    tip: "Our files work with all major print-on-demand platforms!"
  }
];

const FEATURES = [
  {
    title: "No Design Skills Needed",
    description: "Anyone can create professional designs with just text descriptions",
    icon: Lightbulb
  },
  {
    title: "Lightning Fast",
    description: "From idea to finished design in under 30 seconds",
    icon: Zap
  },
  {
    title: "Print-Ready Quality",
    description: "High-resolution files perfect for any printing method",
    icon: CheckCircle
  }
];

const TIPS = [
  "Be specific about colors, objects, and mood in your prompt",
  "Try different styles to see which fits your vision best",
  "Use descriptive adjectives like 'vibrant', 'minimalist', or 'bold'",
  "Mention the overall vibe you want: fun, serious, artistic, etc.",
  "Don't be afraid to regenerate if the first result isn't perfect"
];

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4" variant="secondary">
            How It Works
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            Create T-Shirt Designs in 5 Simple Steps
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            No design experience needed. Our AI-powered tool transforms your ideas into stunning, 
            print-ready T-shirt designs in seconds. Here's exactly how it works:
          </p>
          <Link href="/design">
            <Button size="lg" className="mb-8">
              <Shirt className="mr-2 h-5 w-5" />
              Start Creating Now
            </Button>
          </Link>
        </div>

        {/* Steps Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">The Process</h2>
          <div className="space-y-8">
            {STEPS.map((step, index) => (
              <div key={step.step} className="relative">
                <Card className="max-w-4xl mx-auto">
                  <CardContent className="p-8">
                    <div className="flex flex-col md:flex-row items-center gap-8">
                      {/* Step Number & Icon */}
                      <div className="flex-shrink-0 text-center">
                        <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mb-4">
                          {step.step}
                        </div>
                        <step.icon className="h-8 w-8 text-primary mx-auto" />
                      </div>

                      {/* Content */}
                      <div className="flex-1 text-center md:text-left">
                        <h3 className="text-2xl font-bold mb-3">{step.title}</h3>
                        <p className="text-lg text-muted-foreground mb-4">{step.description}</p>
                        <div className="bg-muted/50 rounded-lg p-4 mb-3">
                          <p className="text-sm font-medium text-muted-foreground mb-1">Example:</p>
                          <p className="text-sm italic">"{step.example}"</p>
                        </div>
                        <div className="flex items-center justify-center md:justify-start gap-2 text-sm text-primary">
                          <Lightbulb className="h-4 w-4" />
                          <span className="font-medium">Tip: {step.tip}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Arrow */}
                {index < STEPS.length - 1 && (
                  <div className="flex justify-center my-6">
                    <ArrowRight className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose AI Shirt Designer?</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {FEATURES.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Tips Section */}
        <div className="mb-20">
          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center text-2xl">Pro Tips for Better Designs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {TIPS.map((tip, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <p className="text-sm">{tip}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Create Your First Design?</h2>
              <p className="text-muted-foreground mb-6">
                Join thousands of creators who are already using AI to bring their T-shirt ideas to life. 
                Start your design journey today - it's free to try!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/design">
                  <Button size="lg">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Start Creating
                  </Button>
                </Link>
                <Link href="/gallery">
                  <Button size="lg" variant="outline">
                    View Examples
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
