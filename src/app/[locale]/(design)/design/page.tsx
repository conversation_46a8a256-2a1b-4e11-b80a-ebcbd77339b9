"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Download, <PERSON>rk<PERSON>, Shirt } from "lucide-react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

const DESIGN_STYLES = [
  { id: "minimalist", name: "Minimalist", description: "Clean and simple designs" },
  { id: "vintage", name: "Vintage", description: "Retro and classic styles" },
  { id: "modern", name: "Modern", description: "Contemporary and trendy" },
  { id: "cartoon", name: "Cartoon", description: "Fun and playful illustrations" },
  { id: "abstract", name: "Abstract", description: "Artistic and creative patterns" },
];

export default function DesignPage() {
  const t = useTranslations();
  const [prompt, setPrompt] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("minimalist");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a design prompt");
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch("/api/ai/generate-design", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          style: selectedStyle,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setGeneratedImages(data.images);
        toast.success("Design generated successfully!");
      } else {
        toast.error(data.message || "Failed to generate design");
      }
    } catch (error) {
      console.error("Generation error:", error);
      toast.error("Failed to generate design");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `ai-tshirt-design-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Design downloaded!");
    } catch (error) {
      toast.error("Failed to download design");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Shirt className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI T-Shirt Designer
            </h1>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Create unique and custom T-shirt designs in seconds with AI. Just describe your idea and watch it come to life.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Design Generator
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Prompt Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Describe your T-shirt design</label>
                <Input
                  placeholder="e.g., A cute cat wearing sunglasses with rainbow background"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>

              {/* Style Selection */}
              <div className="space-y-3">
                <label className="text-sm font-medium">Choose a style</label>
                <div className="grid grid-cols-1 gap-2">
                  {DESIGN_STYLES.map((style) => (
                    <div
                      key={style.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        selectedStyle === style.id
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => setSelectedStyle(style.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{style.name}</div>
                          <div className="text-sm text-muted-foreground">{style.description}</div>
                        </div>
                        {selectedStyle === style.id && (
                          <Badge variant="default">Selected</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Generate Button */}
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !prompt.trim()}
                className="w-full"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate Design
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Results Panel */}
          <Card>
            <CardHeader>
              <CardTitle>Generated Designs</CardTitle>
            </CardHeader>
            <CardContent>
              {generatedImages.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Shirt className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>Your generated designs will appear here</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  {generatedImages.map((imageUrl, index) => (
                    <div
                      key={index}
                      className="relative group cursor-pointer"
                      onClick={() => setSelectedImage(imageUrl)}
                    >
                      <img
                        src={imageUrl}
                        alt={`Generated design ${index + 1}`}
                        className="w-full aspect-square object-cover rounded-lg border"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownload(imageUrl);
                          }}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Selected Image Preview */}
        {selectedImage && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>T-Shirt Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                <div className="relative">
                  {/* T-shirt mockup background */}
                  <div className="w-80 h-96 bg-white rounded-lg shadow-lg flex items-center justify-center border">
                    <img
                      src={selectedImage}
                      alt="Selected design"
                      className="max-w-60 max-h-60 object-contain"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-center mt-6">
                <Button onClick={() => handleDownload(selectedImage)} size="lg">
                  <Download className="h-4 w-4 mr-2" />
                  Download High Quality
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
