import { ReactNode } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shirt, Home, User, CreditCard } from "lucide-react";
import { auth } from "@/auth";
import { getUserCredits } from "@/services/credit";
import { findUserByUuid } from "@/models/user";

export default async function DesignLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();
  let userCredits = null;

  if (session?.user?.id) {
    try {
      const user = await findUserByUuid(session.user.id);
      if (user) {
        userCredits = await getUserCredits(user.uuid);
      }
    } catch (error) {
      console.error("Failed to fetch user credits:", error);
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Top Navigation */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2 font-bold text-xl">
              <Shirt className="h-6 w-6 text-primary" />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                AI Shirt
              </span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                Home
              </Link>
              <Link
                href="/design"
                className="text-sm font-medium text-foreground"
              >
                Design
              </Link>
              <Link
                href="/templates"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                Templates
              </Link>
              <Link
                href="/gallery"
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                Gallery
              </Link>
            </nav>

            {/* User Actions */}
            <div className="flex items-center gap-4">
              {session?.user ? (
                <>
                  {/* Credits Display */}
                  {userCredits && (
                    <div className="hidden sm:flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-full">
                      <CreditCard className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        {userCredits.left_credits} credits
                      </span>
                    </div>
                  )}
                  
                  {/* User Menu */}
                  <Link href="/my-designs">
                    <Button variant="ghost" size="sm">
                      <User className="h-4 w-4 mr-2" />
                      My Designs
                    </Button>
                  </Link>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <Link href="/auth/signin">
                    <Button variant="ghost" size="sm">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/pricing">
                    <Button size="sm">
                      Get Started
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main>{children}</main>
    </div>
  );
}
