"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, Heart, Download, Eye } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";

// Mock template data - in a real app, this would come from an API
const TEMPLATE_CATEGORIES = [
  { id: "all", name: "All Templates", count: 120 },
  { id: "minimalist", name: "Minimalist", count: 25 },
  { id: "vintage", name: "Vintage", count: 18 },
  { id: "modern", name: "Modern", count: 22 },
  { id: "cartoon", name: "Cartoon", count: 30 },
  { id: "abstract", name: "Abstract", count: 15 },
  { id: "typography", name: "Typography", count: 10 },
];

const MOCK_TEMPLATES = [
  {
    id: "1",
    name: "Sunset Vibes",
    category: "minimalist",
    preview: "/api/placeholder/300/300",
    downloads: 1234,
    likes: 89,
    isPremium: false,
  },
  {
    id: "2",
    name: "Retro Gaming",
    category: "vintage",
    preview: "/api/placeholder/300/300",
    downloads: 856,
    likes: 156,
    isPremium: true,
  },
  {
    id: "3",
    name: "Geometric Pattern",
    category: "modern",
    preview: "/api/placeholder/300/300",
    downloads: 2341,
    likes: 203,
    isPremium: false,
  },
  {
    id: "4",
    name: "Cute Animals",
    category: "cartoon",
    preview: "/api/placeholder/300/300",
    downloads: 3456,
    likes: 445,
    isPremium: false,
  },
  {
    id: "5",
    name: "Abstract Flow",
    category: "abstract",
    preview: "/api/placeholder/300/300",
    downloads: 567,
    likes: 78,
    isPremium: true,
  },
  {
    id: "6",
    name: "Bold Typography",
    category: "typography",
    preview: "/api/placeholder/300/300",
    downloads: 890,
    likes: 123,
    isPremium: false,
  },
];

export default function TemplatesPage() {
  const t = useTranslations();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [filteredTemplates, setFilteredTemplates] = useState(MOCK_TEMPLATES);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterTemplates(query, selectedCategory);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    filterTemplates(searchQuery, category);
  };

  const filterTemplates = (query: string, category: string) => {
    let filtered = MOCK_TEMPLATES;

    if (category !== "all") {
      filtered = filtered.filter(template => template.category === category);
    }

    if (query) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    setFilteredTemplates(filtered);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Design Templates
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Choose from hundreds of professionally designed templates and customize them to create your perfect T-shirt design.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Filter Button */}
            <Button variant="outline" className="md:w-auto">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-2">
            {TEMPLATE_CATEGORIES.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryChange(category.id)}
                className="text-sm"
              >
                {category.name}
                <Badge variant="secondary" className="ml-2">
                  {category.count}
                </Badge>
              </Button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-0">
                {/* Template Preview */}
                <div className="relative aspect-square overflow-hidden rounded-t-lg">
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                    <div className="text-6xl text-gray-400">🎨</div>
                  </div>
                  
                  {/* Premium Badge */}
                  {template.isPremium && (
                    <Badge className="absolute top-2 right-2 bg-yellow-500 text-yellow-900">
                      Premium
                    </Badge>
                  )}

                  {/* Hover Actions */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button size="sm" variant="secondary">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Link href={`/design?template=${template.id}`}>
                      <Button size="sm">
                        Use Template
                      </Button>
                    </Link>
                  </div>
                </div>

                {/* Template Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <Download className="h-3 w-3" />
                        {template.downloads.toLocaleString()}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {template.likes}
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {TEMPLATE_CATEGORIES.find(cat => cat.id === template.category)?.name}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button size="lg" variant="outline">
            Load More Templates
          </Button>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Can't find what you're looking for?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-6">
                Use our AI designer to create completely custom designs from scratch. Just describe your idea and let AI bring it to life.
              </p>
              <Link href="/design">
                <Button size="lg">
                  Create Custom Design
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
