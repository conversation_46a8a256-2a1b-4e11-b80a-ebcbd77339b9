"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Download, Eye, Share2, <PERSON>rk<PERSON> } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";

// Mock gallery data
const GALLERY_ITEMS = [
  {
    id: "1",
    title: "Cosmic Cat Adventure",
    style: "cartoon",
    prompt: "A cute cat wearing a space helmet floating among colorful planets and stars",
    likes: 234,
    downloads: 89,
    creator: "<PERSON>",
    createdAt: "2024-01-15",
    featured: true,
  },
  {
    id: "2",
    title: "Minimalist Mountain",
    style: "minimalist",
    prompt: "Simple geometric mountain silhouette with sunset colors",
    likes: 156,
    downloads: 67,
    creator: "<PERSON>",
    createdAt: "2024-01-14",
    featured: false,
  },
  {
    id: "3",
    title: "Retro Gaming Vibes",
    style: "vintage",
    prompt: "8-bit style arcade game characters with neon colors",
    likes: 445,
    downloads: 178,
    creator: "<PERSON>",
    createdAt: "2024-01-13",
    featured: true,
  },
  {
    id: "4",
    title: "Abstract Flow",
    style: "abstract",
    prompt: "Flowing liquid shapes in gradient colors",
    likes: 89,
    downloads: 34,
    creator: "Emma <PERSON>",
    createdAt: "2024-01-12",
    featured: false,
  },
  {
    id: "5",
    title: "Modern Typography",
    style: "modern",
    prompt: "Bold typography with geometric elements",
    likes: 267,
    downloads: 123,
    creator: "David Lee",
    createdAt: "2024-01-11",
    featured: false,
  },
  {
    id: "6",
    title: "Nature's Harmony",
    style: "minimalist",
    prompt: "Simple line art of trees and birds in harmony",
    likes: 178,
    downloads: 56,
    creator: "Lisa Zhang",
    createdAt: "2024-01-10",
    featured: true,
  },
];

const STYLE_FILTERS = [
  { id: "all", name: "All Styles" },
  { id: "minimalist", name: "Minimalist" },
  { id: "vintage", name: "Vintage" },
  { id: "modern", name: "Modern" },
  { id: "cartoon", name: "Cartoon" },
  { id: "abstract", name: "Abstract" },
];

export default function GalleryPage() {
  const t = useTranslations();
  const [selectedStyle, setSelectedStyle] = useState("all");
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const filteredItems = GALLERY_ITEMS.filter(item => {
    const styleMatch = selectedStyle === "all" || item.style === selectedStyle;
    const featuredMatch = !showFeaturedOnly || item.featured;
    return styleMatch && featuredMatch;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Design Gallery
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover amazing T-shirt designs created by our community using AI. Get inspired and create your own unique designs.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 justify-center mb-4">
            {STYLE_FILTERS.map((style) => (
              <Button
                key={style.id}
                variant={selectedStyle === style.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedStyle(style.id)}
              >
                {style.name}
              </Button>
            ))}
          </div>
          
          <div className="flex justify-center">
            <Button
              variant={showFeaturedOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Featured Only
            </Button>
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredItems.map((item) => (
            <Card key={item.id} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
              <CardContent className="p-0">
                {/* Design Preview */}
                <div className="relative aspect-square overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                    <div className="text-6xl">🎨</div>
                  </div>
                  
                  {/* Featured Badge */}
                  {item.featured && (
                    <Badge className="absolute top-2 left-2 bg-yellow-500 text-yellow-900">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}

                  {/* Style Badge */}
                  <Badge variant="secondary" className="absolute top-2 right-2 capitalize">
                    {item.style}
                  </Badge>

                  {/* Hover Actions */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button size="sm" variant="secondary">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button size="sm" variant="secondary">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>

                {/* Design Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-2">{item.title}</h3>
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    "{item.prompt}"
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                    <span>by {item.creator}</span>
                    <span>{new Date(item.createdAt).toLocaleDateString()}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {item.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <Download className="h-3 w-3" />
                        {item.downloads}
                      </span>
                    </div>
                    
                    <Link href={`/design?inspiration=${item.id}`}>
                      <Button size="sm" variant="outline">
                        Create Similar
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mb-16">
          <Button size="lg" variant="outline">
            Load More Designs
          </Button>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Create Your Own?</h2>
              <p className="text-muted-foreground mb-6">
                Join thousands of creators who are using AI to bring their T-shirt design ideas to life. 
                Start creating your unique designs today!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/design">
                  <Button size="lg">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Start Creating
                  </Button>
                </Link>
                <Link href="/templates">
                  <Button size="lg" variant="outline">
                    Browse Templates
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
