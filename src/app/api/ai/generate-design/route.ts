import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { auth } from "@/auth";
import { findUserByUuid } from "@/models/user";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { experimental_generateImage as generateImage } from "ai";
import { openai } from "@ai-sdk/openai";
import { replicate } from "@ai-sdk/replicate";

// Style-specific prompt enhancements
const STYLE_PROMPTS = {
  minimalist: "minimalist, clean, simple, modern, vector art style, solid colors, geometric shapes",
  vintage: "vintage, retro, classic, aged, distressed, nostalgic, muted colors, hand-drawn style",
  modern: "modern, contemporary, trendy, sleek, bold colors, digital art style",
  cartoon: "cartoon, cute, playful, colorful, fun, illustration style, character design",
  abstract: "abstract, artistic, creative, geometric patterns, vibrant colors, modern art style"
};

// Credit costs for different operations
const CREDIT_COSTS = {
  generate: 2, // Cost per image generation
  enhance: 1,  // Cost per prompt enhancement
};

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return respErr("Authentication required", 401);
    }

    const { prompt, style = "minimalist" } = await req.json();
    
    if (!prompt || typeof prompt !== "string" || prompt.trim().length === 0) {
      return respErr("Valid prompt is required");
    }

    if (!STYLE_PROMPTS[style as keyof typeof STYLE_PROMPTS]) {
      return respErr("Invalid style selected");
    }

    // Get user and check credits
    const user = await findUserByUuid(session.user.id);
    if (!user) {
      return respErr("User not found", 404);
    }

    const userCredits = await getUserCredits(user.uuid);
    if (userCredits.left_credits < CREDIT_COSTS.generate) {
      return respErr("Insufficient credits. Please purchase more credits to continue.", 402);
    }

    // Enhance prompt with style-specific keywords
    const styleEnhancement = STYLE_PROMPTS[style as keyof typeof STYLE_PROMPTS];
    const enhancedPrompt = `${prompt}, ${styleEnhancement}, t-shirt design, isolated on white background, high quality, detailed`;

    console.log("Generating design with prompt:", enhancedPrompt);

    // Generate image using AI
    let generatedImages: string[] = [];
    
    try {
      // Try OpenAI DALL-E first
      if (process.env.OPENAI_API_KEY) {
        const { image } = await generateImage({
          model: openai.image("dall-e-3"),
          prompt: enhancedPrompt,
          n: 1,
          size: "1024x1024",
          quality: "hd",
          style: "natural",
        });

        generatedImages.push(image.url);
      } 
      // Fallback to Replicate if OpenAI is not available
      else if (process.env.REPLICATE_API_TOKEN) {
        const { image } = await generateImage({
          model: replicate.image("black-forest-labs/flux-schnell"),
          prompt: enhancedPrompt,
          n: 1,
          aspectRatio: "1:1",
        });

        generatedImages.push(image.url);
      } else {
        return respErr("No AI image generation service configured");
      }

    } catch (aiError) {
      console.error("AI generation error:", aiError);
      return respErr("Failed to generate design. Please try again with a different prompt.");
    }

    // Deduct credits after successful generation
    try {
      await decreaseCredits({
        user_uuid: user.uuid,
        trans_type: CreditsTransType.Ping, // Reusing existing transaction type
        credits: CREDIT_COSTS.generate,
      });
    } catch (creditError) {
      console.error("Failed to deduct credits:", creditError);
      // Continue anyway since image was generated successfully
    }

    // TODO: Save generation history to database
    // await saveDesignGeneration({
    //   user_uuid: user.uuid,
    //   prompt,
    //   style,
    //   result_urls: generatedImages,
    //   credits_used: CREDIT_COSTS.generate,
    // });

    return respData({
      success: true,
      images: generatedImages,
      credits_used: CREDIT_COSTS.generate,
      remaining_credits: Math.max(0, userCredits.left_credits - CREDIT_COSTS.generate),
    });

  } catch (error) {
    console.error("Design generation error:", error);
    return respErr("Internal server error. Please try again later.");
  }
}

// GET endpoint to check user's generation capabilities
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return respErr("Authentication required", 401);
    }

    const user = await findUserByUuid(session.user.id);
    if (!user) {
      return respErr("User not found", 404);
    }

    const userCredits = await getUserCredits(user.uuid);
    
    return respData({
      can_generate: userCredits.left_credits >= CREDIT_COSTS.generate,
      credits: userCredits.left_credits,
      cost_per_generation: CREDIT_COSTS.generate,
      available_styles: Object.keys(STYLE_PROMPTS),
    });

  } catch (error) {
    console.error("Error checking generation capabilities:", error);
    return respErr("Internal server error");
  }
}
