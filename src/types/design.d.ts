export interface DesignTemplate {
  id: number;
  uuid: string;
  name: string;
  description?: string;
  category?: string;
  style?: string;
  preview_url?: string;
  design_data?: string; // JSON string
  created_at?: Date;
  updated_at?: Date;
  is_public: boolean;
  downloads: number;
  likes: number;
}

export interface UserDesign {
  id: number;
  uuid: string;
  user_uuid: string;
  name?: string;
  design_data?: string; // JSON string
  preview_url?: string;
  created_at?: Date;
  updated_at?: Date;
  is_public: boolean;
}

export interface DesignGeneration {
  id: number;
  uuid: string;
  user_uuid: string;
  prompt: string;
  style?: string;
  result_urls?: string; // JSON array of URLs
  credits_used: number;
  created_at?: Date;
}

export interface DesignGenerationRequest {
  prompt: string;
  style: string;
  user_uuid?: string;
}

export interface DesignGenerationResponse {
  success: boolean;
  images: string[];
  credits_used: number;
  remaining_credits: number;
  message?: string;
}

export interface DesignStyle {
  id: string;
  name: string;
  description: string;
  example_prompt?: string;
}

export interface DesignCategory {
  id: string;
  name: string;
  count: number;
}

export interface GalleryItem {
  id: string;
  title: string;
  style: string;
  prompt: string;
  preview_url: string;
  likes: number;
  downloads: number;
  creator: string;
  created_at: string;
  featured: boolean;
}

export interface TemplateSearchParams {
  query?: string;
  category?: string;
  style?: string;
  page?: number;
  limit?: number;
}

export interface DesignExportOptions {
  format: 'png' | 'jpg' | 'svg';
  quality: 'standard' | 'high' | 'ultra';
  size: 'small' | 'medium' | 'large' | 'custom';
  width?: number;
  height?: number;
}
