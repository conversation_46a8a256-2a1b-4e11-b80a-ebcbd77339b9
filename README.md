# AI Shirt Designer - AI-Powered T-Shirt Design Generator

🎨 **Create unique and custom T-shirt designs in seconds with AI**

Transform your ideas into stunning T-shirt designs using advanced artificial intelligence. No design experience needed - just describe your vision and watch it come to life instantly.

![AI T-Shirt Design Generator](preview.png)

## 🚀 Features

- **AI-Powered Design Generation** - Transform text prompts into unique T-shirt designs
- **Multiple Art Styles** - Choose from minimalist, vintage, modern, cartoon, and abstract styles
- **Instant Preview** - See your designs on realistic T-shirt mockups
- **High-Quality Export** - Download designs in high resolution, perfect for printing
- **Template Library** - Access hundreds of pre-made templates
- **Easy to Use** - No design experience required
- **Multi-language Support** - Available in English and Chinese
- **Credit System** - Fair usage with built-in credit management
- **Responsive Design** - Works perfectly on desktop and mobile

## 🎯 Perfect For

- **Print-on-Demand Sellers** - Create unique designs for your online store
- **Small Business Owners** - Design branded merchandise for your company
- **Content Creators** - Generate eye-catching designs for your audience
- **Fashion Enthusiasts** - Bring your creative ideas to life
- **Anyone** - No design skills needed, just creativity!

## 🛠️ Quick Start

1. **Clone the repository**
```bash
git clone https://github.com/yourusername/ai-shirt-designer.git
cd ai-shirt-designer
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

4. **Configure AI services** (add your API keys to `.env.local`)
```bash
# OpenAI for DALL-E
OPENAI_API_KEY=your_openai_api_key

# Alternative: Replicate for other models
REPLICATE_API_TOKEN=your_replicate_token
```

5. **Run the development server**
```bash
pnpm dev
```

6. **Open your browser**
Visit [http://localhost:3000](http://localhost:3000) and start creating!

## 🎨 How to Use

1. **Enter your design idea** - Describe what you want on your T-shirt
2. **Choose a style** - Select from our available art styles
3. **Generate design** - Click generate and watch AI create your design
4. **Preview on T-shirt** - See how it looks on an actual T-shirt
5. **Download** - Get your high-quality design file

## 🔧 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **AI Integration**: OpenAI DALL-E, Replicate
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js
- **Payments**: Stripe
- **Deployment**: Vercel/Cloudflare

## 📦 Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fshipanyai%2Fshipany-template-one&project-name=my-shipany-project&repository-name=my-shipany-project&redirect-url=https%3A%2F%2Fshipany.ai&demo-title=ShipAny&demo-description=Ship%20Any%20AI%20Startup%20in%20hours%2C%20not%20days&demo-url=https%3A%2F%2Fshipany.ai&demo-image=https%3A%2F%2Fpbs.twimg.com%2Fmedia%2FGgGSW3La8AAGJgU%3Fformat%3Djpg%26name%3Dlarge)

- Deploy to Cloudflare

for new project, clone with branch "cloudflare"

```shell
git clone -b cloudflare https://github.com/shipanyai/shipany-template-one.git
```

for exist project, checkout to branch "cloudflare"

```shell
git checkout cloudflare
```

1. Customize your environment variables

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

edit your environment variables in `.env.production`

and put all the environment variables under `[vars]` in `wrangler.toml`

2. Deploy

```bash
npm run cf:deploy
```

## Community

- [ShipAny](https://shipany.ai)
- [Documentation](https://docs.shipany.ai)

## License

- [ShipAny AI SaaS Boilerplate License Agreement](LICENSE)
